#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档转换工具
将input目录下的doc和docx文件转换为markdown格式
"""

import os
import sys
from pathlib import Path
import mammoth
from docx import Document
import re
import zipfile
import xml.etree.ElementTree as ET

def clean_markdown_text(text):
    """清理markdown文本，移除多余的空行和格式问题"""
    # 移除多余的空行
    text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)
    # 清理行首行尾空格
    lines = [line.strip() for line in text.split('\n')]
    # 重新组合，保持段落结构
    cleaned_lines = []
    for line in lines:
        if line or (cleaned_lines and cleaned_lines[-1]):  # 保留有内容的行和段落间的空行
            cleaned_lines.append(line)
    
    return '\n'.join(cleaned_lines)

def fix_and_convert_corrupted_docx(file_path, output_path):
    """尝试修复损坏的docx文件并提取文本内容"""
    try:
        print(f"尝试修复损坏的文件: {file_path.name}")
        
        # docx文件本质上是一个zip文件
        with zipfile.ZipFile(file_path, 'r') as zip_ref:
            # 查找document.xml文件
            document_xml = None
            for file_name in zip_ref.namelist():
                if 'document.xml' in file_name:
                    document_xml = file_name
                    break
            
            if document_xml:
                # 读取document.xml内容
                with zip_ref.open(document_xml) as xml_file:
                    content = xml_file.read()
                    
                # 解析XML并提取文本
                try:
                    root = ET.fromstring(content)
                    # 提取所有文本节点
                    text_content = []
                    for elem in root.iter():
                        if elem.text and elem.text.strip():
                            text_content.append(elem.text.strip())
                    
                    # 过滤空文本并组合
                    filtered_text = [text for text in text_content if text and len(text) > 1]
                    markdown_content = '\n\n'.join(filtered_text)
                    
                    # 写入输出文件
                    with open(output_path, 'w', encoding='utf-8') as md_file:
                        md_file.write(markdown_content)
                    
                    return True
                    
                except ET.ParseError as e:
                    print(f"XML解析错误: {e}")
                    return False
            else:
                print("未找到document.xml文件")
                return False
                
    except Exception as e:
        print(f"修复失败: {e}")
        return False

def convert_docx_to_markdown_mammoth(docx_path, output_path):
    """使用mammoth库将docx转换为markdown"""
    try:
        with open(docx_path, "rb") as docx_file:
            result = mammoth.convert_to_markdown(docx_file)
            markdown_content = result.value
            
            # 清理markdown内容
            markdown_content = clean_markdown_text(markdown_content)
            
            # 写入输出文件
            with open(output_path, 'w', encoding='utf-8') as md_file:
                md_file.write(markdown_content)
            
            if result.messages:
                print(f"转换警告 ({docx_path}): {result.messages}")
            
            return True
    except Exception as e:
        print(f"使用mammoth转换失败 {docx_path}: {str(e)}")
        return False

def convert_docx_to_markdown_docx(docx_path, output_path):
    """使用python-docx库将docx转换为markdown（备用方法）"""
    try:
        doc = Document(docx_path)
        markdown_content = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:
                # 简单的样式处理
                if paragraph.style.name.startswith('Heading'):
                    level = int(paragraph.style.name.split()[-1]) if paragraph.style.name.split()[-1].isdigit() else 1
                    markdown_content.append('#' * level + ' ' + text)
                else:
                    markdown_content.append(text)
                markdown_content.append('')  # 添加空行
        
        # 处理表格
        for table in doc.tables:
            markdown_content.append('')  # 表格前空行
            for i, row in enumerate(table.rows):
                row_data = []
                for cell in row.cells:
                    cell_text = cell.text.strip().replace('\n', ' ')
                    row_data.append(cell_text)
                
                # 创建markdown表格行
                markdown_content.append('| ' + ' | '.join(row_data) + ' |')
                
                # 添加表头分隔符
                if i == 0:
                    separator = '| ' + ' | '.join(['---'] * len(row_data)) + ' |'
                    markdown_content.append(separator)
            
            markdown_content.append('')  # 表格后空行
        
        # 清理并写入文件
        final_content = clean_markdown_text('\n'.join(markdown_content))
        
        with open(output_path, 'w', encoding='utf-8') as md_file:
            md_file.write(final_content)
        
        return True
    except Exception as e:
        print(f"使用python-docx转换失败 {docx_path}: {str(e)}")
        return False

def convert_documents():
    """主转换函数"""
    input_dir = Path("input")
    output_dir = Path("output")
    
    # 创建输出目录
    output_dir.mkdir(exist_ok=True)
    
    if not input_dir.exists():
        print("错误: input目录不存在")
        return
    
    # 支持的文件扩展名
    supported_extensions = ['.docx', '.doc']
    
    # 获取所有需要转换的文件
    files_to_convert = []
    for ext in supported_extensions:
        files_to_convert.extend(input_dir.glob(f"*{ext}"))
    
    if not files_to_convert:
        print("在input目录中没有找到doc或docx文件")
        return
    
    print(f"找到 {len(files_to_convert)} 个文件需要转换")
    
    success_count = 0
    failed_files = []
    
    for file_path in files_to_convert:
        print(f"\n正在转换: {file_path.name}")
        
        # 生成输出文件名
        output_filename = file_path.stem + '.md'
        output_path = output_dir / output_filename
        
        # 尝试转换
        success = False
        
        if file_path.suffix.lower() == '.docx':
            # 首先尝试使用mammoth（更好的转换质量）
            success = convert_docx_to_markdown_mammoth(file_path, output_path)
            
            # 如果mammoth失败，尝试使用python-docx
            if not success:
                print(f"尝试备用方法转换: {file_path.name}")
                success = convert_docx_to_markdown_docx(file_path, output_path)
            
            # 如果还是失败，尝试修复方法
            if not success:
                print(f"尝试修复方法转换: {file_path.name}")
                success = fix_and_convert_corrupted_docx(file_path, output_path)
        
        elif file_path.suffix.lower() == '.doc':
            print(f"警告: .doc格式需要额外的库支持，跳过 {file_path.name}")
            print("建议先将.doc文件转换为.docx格式")
            failed_files.append(file_path.name)
            continue
        
        if success:
            success_count += 1
            print(f"✓ 成功转换: {output_filename}")
        else:
            failed_files.append(file_path.name)
            print(f"✗ 转换失败: {file_path.name}")
    
    # 输出转换结果统计
    print(f"\n转换完成!")
    print(f"成功转换: {success_count} 个文件")
    print(f"转换失败: {len(failed_files)} 个文件")
    
    if failed_files:
        print("\n失败的文件:")
        for filename in failed_files:
            print(f"  - {filename}")
    
    print(f"\n转换后的markdown文件保存在: {output_dir.absolute()}")

if __name__ == "__main__":
    print("文档转换工具 - Doc/Docx to Markdown")
    print("=" * 50)
    
    try:
        convert_documents()
    except KeyboardInterrupt:
        print("\n\n转换被用户中断")
    except Exception as e:
        print(f"\n发生错误: {str(e)}")
        sys.exit(1) 