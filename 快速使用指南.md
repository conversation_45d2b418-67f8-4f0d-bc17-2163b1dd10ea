# 专利分析工具快速使用指南

## 🚀 快速开始

### 第一步：获取API密钥
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 登录Google账号
3. 点击"Create API Key"创建新密钥
4. 复制生成的API密钥

### 第二步：配置API密钥
1. 打开 `config.json` 文件
2. 将您的API密钥填入 `gemini_api_key` 字段：
```json
{
  "gemini_api_key": "your_api_key_here"
}
```

### 第三步：准备专利文档
- 将需要分析的专利文档（.docx格式）放入 `input` 文件夹
- 确保文档格式正确，内容完整

### 第四步：运行分析
**方法一：使用批处理脚本（推荐）**
```bash
双击运行 run_analysis.bat
```

**方法二：使用命令行**
```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行测试
python test_analyzer.py

# 3. 开始分析
python patent_analyzer_gemini.py
```

## 📊 结果查看

分析完成后，结果保存在以下目录：

### 📁 gemini_analysis/
详细分析报告，包含：
- 技术创新性评分
- 新颖性分析
- 技术优势和不足
- 具体改进建议

### 📁 gemini_optimized/
优化后的专利内容：
- 改进的技术描述
- 增强的创新点表达
- 完善的技术方案

### 📁 gemini_reports/
汇总报告：
- Excel格式的评分对比表
- Markdown格式的详细报告
- 排名和统计分析

## ⚙️ 配置说明

### 基本配置 (config.json)
```json
{
  "gemini_api_key": "your_api_key",      // 必填：API密钥
  "model_name": "gemini-1.5-pro",       // 模型名称
  "input_directory": "input",            // 输入目录
  "max_retries": 3,                      // 重试次数
  "delay_between_requests": 1            // 请求间隔(秒)
}
```

### 高级配置选项
- `temperature`: 控制输出的随机性 (0.0-1.0)
- `max_output_tokens`: 最大输出长度
- `batch_size`: 批处理大小

## 🔧 故障排除

### 常见问题

**1. API密钥错误**
```
错误信息：API key not valid
解决方案：检查config.json中的API密钥是否正确
```

**2. 网络连接问题**
```
错误信息：Connection failed
解决方案：检查网络连接，确认可以访问Google服务
```

**3. 文档读取失败**
```
错误信息：无法读取文件内容
解决方案：确认文档格式为.docx，文件未损坏
```

**4. 内存不足**
```
错误信息：Memory error
解决方案：减少batch_size，分批处理文档
```

### 日志查看
查看 `patent_analysis.log` 文件获取详细错误信息

## 📈 评分标准

### 技术创新性 (0-10分)
- **9-10分**：突破性创新，技术领先
- **7-8分**：显著创新，技术先进  
- **5-6分**：一般创新，技术改进
- **3-4分**：微小创新，技术组合
- **1-2分**：缺乏创新，现有技术

### 新颖性 (0-10分)
- **9-10分**：完全新颖，无相似技术
- **7-8分**：高度新颖，差异显著
- **5-6分**：中等新颖，有一定差异
- **3-4分**：新颖性一般，差异较小
- **1-2分**：新颖性不足，相似技术较多

### 完整性 (0-10分)
- **9-10分**：方案完整，逻辑清晰，可行性强
- **7-8分**：方案较完整，逻辑合理
- **5-6分**：方案基本完整，有待完善
- **3-4分**：方案不够完整，逻辑有缺陷
- **1-2分**：方案不完整，可行性差

## 💡 使用技巧

### 1. 提高分析质量
- 确保专利文档内容完整、格式规范
- 避免包含大量图片或特殊格式
- 技术描述要清晰、逻辑性强

### 2. 批量处理优化
- 合理设置请求间隔，避免API限制
- 大量文档建议分批处理
- 监控API使用量，避免超出配额

### 3. 结果解读
- AI分析结果仅供参考，需要专业验证
- 结合人工审核进行最终评估
- 关注具体的改进建议和优化方案

## 📞 技术支持

如遇到问题：
1. 查看日志文件：`patent_analysis.log`
2. 运行测试脚本：`python test_analyzer.py`
3. 检查配置文件：`config.json`
4. 确认网络连接和API服务状态

## 🔄 版本更新

定期检查更新：
- 关注新版本发布
- 更新依赖库版本
- 查看新功能和改进

---

**祝您使用愉快！如有问题，请查看详细文档 README_Gemini.md**
