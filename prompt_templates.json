{"default_analysis": {"name": "默认专利分析模板", "description": "标准的专利分析模板，适用于大多数技术领域", "prompt": "你是一位资深的专利分析专家，请对以下专利文档进行全面分析...", "focus_areas": ["技术创新性", "新颖性", "实用性", "可实施性"]}, "software_patent": {"name": "软件专利分析模板", "description": "专门针对软件和算法类专利的分析模板", "prompt": "作为软件专利专家，请重点关注以下专利的算法创新性、技术实现方案和软件架构设计...", "focus_areas": ["算法创新性", "软件架构", "技术实现", "用户体验"]}, "hardware_patent": {"name": "硬件专利分析模板", "description": "专门针对硬件和机械类专利的分析模板", "prompt": "作为硬件专利专家，请重点分析以下专利的机械结构、硬件设计和制造工艺...", "focus_areas": ["结构创新性", "制造工艺", "材料应用", "成本效益"]}, "biotech_patent": {"name": "生物技术专利分析模板", "description": "专门针对生物技术和医药类专利的分析模板", "prompt": "作为生物技术专利专家，请重点评估以下专利的生物学原理、临床应用和安全性...", "focus_areas": ["生物学原理", "临床应用", "安全性评估", "监管合规"]}, "ai_patent": {"name": "人工智能专利分析模板", "description": "专门针对AI和机器学习类专利的分析模板", "prompt": "作为AI专利专家，请重点分析以下专利的机器学习算法、数据处理方法和AI应用场景...", "focus_areas": ["算法创新", "数据处理", "模型架构", "应用场景"]}, "evaluation_criteria": {"innovation_levels": {"breakthrough": {"score_range": "9-10", "description": "突破性创新，开创新的技术领域或解决方案"}, "significant": {"score_range": "7-8", "description": "显著创新，在现有技术基础上有重要改进"}, "moderate": {"score_range": "5-6", "description": "中等创新，技术改进明显但不够突出"}, "incremental": {"score_range": "3-4", "description": "渐进式创新，小幅度的技术改进"}, "minimal": {"score_range": "1-2", "description": "创新性不足，主要是现有技术的组合"}}, "novelty_levels": {"completely_novel": {"score_range": "9-10", "description": "完全新颖，未发现相似的现有技术"}, "highly_novel": {"score_range": "7-8", "description": "高度新颖，与现有技术有显著差异"}, "moderately_novel": {"score_range": "5-6", "description": "中等新颖，有一定的技术差异"}, "somewhat_novel": {"score_range": "3-4", "description": "新颖性一般，与现有技术差异较小"}, "not_novel": {"score_range": "1-2", "description": "新颖性不足，存在相似的现有技术"}}}, "output_formats": {"detailed_report": {"sections": ["执行摘要", "技术分析", "创新性评估", "新颖性分析", "现有技术对比", "实施可行性", "市场价值评估", "改进建议", "专利策略建议"]}, "summary_report": {"sections": ["核心评分", "主要优势", "关键不足", "改进建议"]}}, "customization_options": {"industry_focus": ["信息技术", "生物医药", "机械制造", "新能源", "新材料", "人工智能", "物联网", "区块链"], "analysis_depth": ["快速评估", "标准分析", "深度分析", "专家级分析"], "output_language": ["中文", "英文", "双语"]}}