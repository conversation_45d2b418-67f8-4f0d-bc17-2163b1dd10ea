# PowerShell Script: Batch convert .doc to .docx
Write-Host "Batch converting .doc files to .docx format" -ForegroundColor Green
Write-Host "============================================" -ForegroundColor Green

try {
    # Create Word application object
    $word = New-Object -ComObject Word.Application
    $word.Visible = $false
    
    # Get all .doc files in current directory
    $docFiles = Get-ChildItem -Path "." -Filter "*.doc"
    
    if ($docFiles.Count -eq 0) {
        Write-Host "No .doc files found in current directory" -ForegroundColor Yellow
        exit
    }
    
    Write-Host "Found $($docFiles.Count) .doc files" -ForegroundColor Cyan
    
    foreach ($file in $docFiles) {
        try {
            Write-Host "Converting: $($file.Name)" -ForegroundColor White
            
            # Open document
            $doc = $word.Documents.Open($file.FullName)
            
            # Generate new filename
            $newName = $file.FullName -replace "\.doc$", ".docx"
            
            # Save as docx format (format code 16 = wdFormatXMLDocument)
            $doc.SaveAs2($newName, 16)
            
            # Close document
            $doc.Close()
            
            Write-Host "  Success: $([System.IO.Path]::GetFileName($newName))" -ForegroundColor Green
        }
        catch {
            Write-Host "  Failed: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}
catch {
    Write-Host "Error: Cannot start Microsoft Word. Please ensure Microsoft Office is installed." -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
}
finally {
    # Ensure Word application is closed
    if ($word) {
        $word.Quit()
        [System.Runtime.Interopservices.Marshal]::ReleaseComObject($word) | Out-Null
    }
}

Write-Host "Conversion completed!" -ForegroundColor Green
Write-Host "Now you can run: python document_converter.py" -ForegroundColor Cyan
Read-Host "Press Enter to exit"
