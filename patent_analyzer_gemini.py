#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专利分析优化工具 - 基于Google Gemini API
功能：分析input文件夹中的专利文档，生成优化建议和改进版本
作者：AI Assistant
日期：2024
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# 第三方库导入
try:
    import google.generativeai as genai
    from docx import Document
    import pandas as pd
    from tqdm import tqdm
except ImportError as e:
    print(f"缺少必要的依赖库: {e}")
    print("请运行: pip install google-generativeai python-docx pandas tqdm")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('patent_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class PatentAnalysisResult:
    """专利分析结果数据类"""
    filename: str
    innovation_score: float
    novelty_score: float
    completeness_score: float
    overall_score: float
    strengths: List[str]
    weaknesses: List[str]
    recommendations: List[str]
    optimized_content: str
    analysis_summary: str

class PatentAnalyzer:
    """专利分析器主类"""

    def __init__(self, api_key: str, model_name: str = "gemini-1.5-pro"):
        """
        初始化专利分析器

        Args:
            api_key: Google Gemini API密钥
            model_name: 使用的模型名称
        """
        self.api_key = api_key
        self.model_name = model_name
        self.model = None
        self._setup_gemini()

        # 创建输出目录
        self.output_dirs = {
            'analysis': Path('gemini_analysis'),
            'optimized': Path('gemini_optimized'),
            'reports': Path('gemini_reports')
        }
        for dir_path in self.output_dirs.values():
            dir_path.mkdir(exist_ok=True)

    def _setup_gemini(self):
        """配置Gemini API"""
        try:
            genai.configure(api_key=self.api_key)
            self.model = genai.GenerativeModel(self.model_name)
            logger.info(f"成功配置Gemini API，使用模型: {self.model_name}")
        except Exception as e:
            logger.error(f"配置Gemini API失败: {e}")
            raise

    def read_docx_content(self, file_path: Path) -> str:
        """
        读取docx文件内容

        Args:
            file_path: 文件路径

        Returns:
            文档文本内容
        """
        try:
            doc = Document(file_path)
            content = []

            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content.append(paragraph.text.strip())

            # 处理表格内容
            for table in doc.tables:
                for row in table.rows:
                    row_text = []
                    for cell in row.cells:
                        if cell.text.strip():
                            row_text.append(cell.text.strip())
                    if row_text:
                        content.append(" | ".join(row_text))

            return "\n".join(content)

        except Exception as e:
            logger.error(f"读取文档 {file_path} 失败: {e}")
            return ""

    def create_analysis_prompt(self, patent_content: str) -> str:
        """
        创建专利分析的prompt模板

        Args:
            patent_content: 专利文档内容

        Returns:
            格式化的prompt
        """
        prompt = f"""
你是一位资深的专利分析专家，请对以下专利文档进行全面分析。请按照以下结构提供详细的分析报告：

## 专利文档内容：
{patent_content}

## 分析要求：
请从以下维度对该专利进行深入分析，并以JSON格式返回结果：

### 1. 技术创新性评估 (0-10分)
- 分析核心技术特征的创新程度
- 评估技术突破性和先进性
- 与现有技术的差异化程度

### 2. 新颖性分析 (0-10分)
- 技术方案的新颖程度
- 是否存在相似的现有技术
- 技术组合的独特性

### 3. 技术方案完整性 (0-10分)
- 技术方案的逻辑完整性
- 实施可行性评估
- 技术细节的充分性

### 4. 现有技术对比
- 识别相关的现有技术
- 分析技术优势和劣势
- 竞争技术的对比

### 5. 改进建议
- 技术方案的具体改进建议
- 专利申请策略建议
- 技术实现优化建议

## 输出格式要求：
请严格按照以下JSON格式返回分析结果：

```json
{
    "innovation_score": 数值(0-10),
    "novelty_score": 数值(0-10),
    "completeness_score": 数值(0-10),
    "overall_score": 数值(0-10),
    "strengths": ["优势1", "优势2", "优势3"],
    "weaknesses": ["不足1", "不足2", "不足3"],
    "recommendations": ["建议1", "建议2", "建议3"],
    "analysis_summary": "详细的分析总结(500-800字)",
    "optimized_content": "优化后的专利内容(保持原有结构，但改进技术描述、增强创新点表达、完善技术方案)"
}
```

请确保分析客观、专业，建议具体可行。优化后的内容应保持专利文档的专业性和完整性。
"""
        return prompt

    def analyze_patent_with_gemini(self, patent_content: str, max_retries: int = 3) -> Optional[Dict]:
        """
        使用Gemini API分析专利

        Args:
            patent_content: 专利内容
            max_retries: 最大重试次数

        Returns:
            分析结果字典
        """
        prompt = self.create_analysis_prompt(patent_content)

        for attempt in range(max_retries):
            try:
                logger.info(f"正在调用Gemini API进行分析 (尝试 {attempt + 1}/{max_retries})")

                response = self.model.generate_content(
                    prompt,
                    generation_config=genai.types.GenerationConfig(
                        temperature=0.3,
                        max_output_tokens=8192,
                    )
                )

                if response.text:
                    # 尝试解析JSON响应
                    try:
                        # 提取JSON部分
                        json_start = response.text.find('{')
                        json_end = response.text.rfind('}') + 1

                        if json_start != -1 and json_end != -1:
                            json_str = response.text[json_start:json_end]
                            result = json.loads(json_str)
                            logger.info("成功解析Gemini API响应")
                            return result
                        else:
                            logger.warning("响应中未找到有效的JSON格式")

                    except json.JSONDecodeError as e:
                        logger.warning(f"JSON解析失败: {e}")
                        # 如果JSON解析失败，尝试手动解析关键信息
                        return self._parse_text_response(response.text)

            except Exception as e:
                logger.error(f"Gemini API调用失败 (尝试 {attempt + 1}): {e}")
                if attempt < max_retries - 1:
                    time.sleep(2 ** attempt)  # 指数退避

        logger.error("所有重试均失败，无法获取分析结果")
        return None

    def _parse_text_response(self, response_text: str) -> Dict:
        """
        手动解析文本响应（当JSON解析失败时的备用方案）

        Args:
            response_text: 响应文本

        Returns:
            解析后的结果字典
        """
        # 这是一个简化的解析器，实际使用中可能需要更复杂的逻辑
        result = {
            "innovation_score": 5.0,
            "novelty_score": 5.0,
            "completeness_score": 5.0,
            "overall_score": 5.0,
            "strengths": ["需要进一步分析"],
            "weaknesses": ["响应解析失败"],
            "recommendations": ["建议重新分析"],
            "analysis_summary": response_text[:500] + "...",
            "optimized_content": "优化内容解析失败，请查看原始响应"
        }

        logger.warning("使用备用解析器处理响应")
        return result

    def process_single_patent(self, file_path: Path) -> Optional[PatentAnalysisResult]:
        """
        处理单个专利文件

        Args:
            file_path: 专利文件路径

        Returns:
            分析结果对象
        """
        logger.info(f"开始处理专利文件: {file_path.name}")

        # 读取文档内容
        content = self.read_docx_content(file_path)
        if not content:
            logger.error(f"无法读取文件内容: {file_path}")
            return None

        # 调用Gemini API分析
        analysis_result = self.analyze_patent_with_gemini(content)
        if not analysis_result:
            logger.error(f"分析失败: {file_path}")
            return None

        # 创建结果对象
        result = PatentAnalysisResult(
            filename=file_path.name,
            innovation_score=float(analysis_result.get('innovation_score', 0)),
            novelty_score=float(analysis_result.get('novelty_score', 0)),
            completeness_score=float(analysis_result.get('completeness_score', 0)),
            overall_score=float(analysis_result.get('overall_score', 0)),
            strengths=analysis_result.get('strengths', []),
            weaknesses=analysis_result.get('weaknesses', []),
            recommendations=analysis_result.get('recommendations', []),
            optimized_content=analysis_result.get('optimized_content', ''),
            analysis_summary=analysis_result.get('analysis_summary', '')
        )

        logger.info(f"成功分析专利: {file_path.name}")
        return result

    def save_analysis_result(self, result: PatentAnalysisResult):
        """
        保存分析结果到文件

        Args:
            result: 分析结果对象
        """
        base_name = Path(result.filename).stem

        # 保存详细分析报告
        analysis_file = self.output_dirs['analysis'] / f"{base_name}_分析报告.md"
        with open(analysis_file, 'w', encoding='utf-8') as f:
            f.write(f"# 专利分析报告：{base_name}\n\n")
            f.write(f"## 分析时间\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"## 评分结果\n")
            f.write(f"- **技术创新性**: {result.innovation_score:.1f}/10\n")
            f.write(f"- **新颖性**: {result.novelty_score:.1f}/10\n")
            f.write(f"- **完整性**: {result.completeness_score:.1f}/10\n")
            f.write(f"- **综合评分**: {result.overall_score:.1f}/10\n\n")

            f.write(f"## 技术优势\n")
            for i, strength in enumerate(result.strengths, 1):
                f.write(f"{i}. {strength}\n")
            f.write("\n")

            f.write(f"## 存在不足\n")
            for i, weakness in enumerate(result.weaknesses, 1):
                f.write(f"{i}. {weakness}\n")
            f.write("\n")

            f.write(f"## 改进建议\n")
            for i, recommendation in enumerate(result.recommendations, 1):
                f.write(f"{i}. {recommendation}\n")
            f.write("\n")

            f.write(f"## 详细分析\n")
            f.write(f"{result.analysis_summary}\n\n")

        # 保存优化后的专利内容
        optimized_file = self.output_dirs['optimized'] / f"{base_name}_优化版本.md"
        with open(optimized_file, 'w', encoding='utf-8') as f:
            f.write(f"# 优化后的专利文档：{base_name}\n\n")
            f.write(f"## 优化时间\n{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"## 优化内容\n\n")
            f.write(result.optimized_content)

        logger.info(f"已保存分析结果: {analysis_file}")
        logger.info(f"已保存优化内容: {optimized_file}")

    def process_all_patents(self, input_dir: str = "input") -> List[PatentAnalysisResult]:
        """
        批量处理所有专利文件

        Args:
            input_dir: 输入目录路径

        Returns:
            所有分析结果的列表
        """
        input_path = Path(input_dir)
        if not input_path.exists():
            logger.error(f"输入目录不存在: {input_path}")
            return []

        # 查找所有docx文件
        docx_files = list(input_path.glob("*.docx"))
        if not docx_files:
            logger.warning(f"在 {input_path} 中未找到docx文件")
            return []

        logger.info(f"找到 {len(docx_files)} 个专利文件")

        results = []

        # 使用进度条处理文件
        for file_path in tqdm(docx_files, desc="处理专利文件"):
            try:
                result = self.process_single_patent(file_path)
                if result:
                    self.save_analysis_result(result)
                    results.append(result)
                else:
                    logger.error(f"处理失败: {file_path}")

                # 添加延迟避免API限制
                time.sleep(1)

            except Exception as e:
                logger.error(f"处理文件 {file_path} 时发生错误: {e}")
                continue

        logger.info(f"成功处理 {len(results)} 个专利文件")
        return results

    def generate_summary_report(self, results: List[PatentAnalysisResult]):
        """
        生成汇总报告

        Args:
            results: 所有分析结果
        """
        if not results:
            logger.warning("没有分析结果，无法生成汇总报告")
            return

        # 创建汇总数据
        summary_data = []
        for result in results:
            summary_data.append({
                '专利名称': result.filename,
                '技术创新性': result.innovation_score,
                '新颖性': result.novelty_score,
                '完整性': result.completeness_score,
                '综合评分': result.overall_score,
                '主要优势': '; '.join(result.strengths[:3]),
                '主要不足': '; '.join(result.weaknesses[:3])
            })

        # 保存Excel汇总报告
        df = pd.DataFrame(summary_data)
        excel_file = self.output_dirs['reports'] / f"专利分析汇总报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        df.to_excel(excel_file, index=False, engine='openpyxl')

        # 生成Markdown汇总报告
        md_file = self.output_dirs['reports'] / f"专利分析汇总报告_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write("# 专利分析汇总报告\n\n")
            f.write(f"## 分析概况\n")
            f.write(f"- **分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"- **分析专利数量**: {len(results)}\n")
            f.write(f"- **平均创新性评分**: {sum(r.innovation_score for r in results) / len(results):.2f}\n")
            f.write(f"- **平均新颖性评分**: {sum(r.novelty_score for r in results) / len(results):.2f}\n")
            f.write(f"- **平均完整性评分**: {sum(r.completeness_score for r in results) / len(results):.2f}\n")
            f.write(f"- **平均综合评分**: {sum(r.overall_score for r in results) / len(results):.2f}\n\n")

            f.write("## 详细评分表\n\n")
            f.write("| 专利名称 | 创新性 | 新颖性 | 完整性 | 综合评分 |\n")
            f.write("|---------|--------|--------|--------|----------|\n")
            for result in results:
                f.write(f"| {result.filename} | {result.innovation_score:.1f} | {result.novelty_score:.1f} | {result.completeness_score:.1f} | {result.overall_score:.1f} |\n")

            f.write("\n## 排名分析\n\n")
            # 按综合评分排序
            sorted_results = sorted(results, key=lambda x: x.overall_score, reverse=True)
            f.write("### 综合评分排名\n")
            for i, result in enumerate(sorted_results, 1):
                f.write(f"{i}. **{result.filename}** - {result.overall_score:.1f}分\n")

        logger.info(f"已生成汇总报告: {excel_file}")
        logger.info(f"已生成汇总报告: {md_file}")


def load_config() -> Dict:
    """
    加载配置文件

    Returns:
        配置字典
    """
    config_file = Path("config.json")

    # 默认配置
    default_config = {
        "gemini_api_key": "AIzaSyBRD92F9M-WPkhwmvzuXzcjslQq3VzPxpk",
        "model_name": "gemini-2.5-flash-preview-05-20",
        "input_directory": "input",
        "max_retries": 3,
        "delay_between_requests": 1,
        "batch_size": 5
    }

    if config_file.exists():
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 合并默认配置
                default_config.update(config)
                return default_config
        except Exception as e:
            logger.warning(f"读取配置文件失败: {e}，使用默认配置")
    else:
        # 创建示例配置文件
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(default_config, f, indent=2, ensure_ascii=False)
        logger.info(f"已创建配置文件: {config_file}")

    return default_config


def main():
    """主函数"""
    print("=" * 60)
    print("专利分析优化工具 - 基于Google Gemini API")
    print("=" * 60)

    # 加载配置
    config = load_config()

    # 检查API密钥
    api_key = config.get("gemini_api_key")
    if not api_key:
        print("\n❌ 错误：未配置Gemini API密钥")
        print("请在config.json文件中设置您的API密钥")
        print("获取API密钥：https://makersuite.google.com/app/apikey")
        return

    try:
        # 初始化分析器
        print(f"\n🚀 初始化专利分析器...")
        analyzer = PatentAnalyzer(
            api_key=api_key,
            model_name=config.get("model_name", "gemini-1.5-pro")
        )

        # 处理所有专利文件
        print(f"\n📁 开始处理专利文件...")
        results = analyzer.process_all_patents(config.get("input_directory", "input"))

        if not results:
            print("❌ 没有成功处理任何专利文件")
            return

        # 生成汇总报告
        print(f"\n📊 生成汇总报告...")
        analyzer.generate_summary_report(results)

        # 显示处理结果
        print(f"\n✅ 处理完成！")
        print(f"📈 成功分析 {len(results)} 个专利文件")
        print(f"📂 分析报告保存在: gemini_analysis/")
        print(f"📝 优化内容保存在: gemini_optimized/")
        print(f"📋 汇总报告保存在: gemini_reports/")

        # 显示评分统计
        if results:
            avg_innovation = sum(r.innovation_score for r in results) / len(results)
            avg_novelty = sum(r.novelty_score for r in results) / len(results)
            avg_completeness = sum(r.completeness_score for r in results) / len(results)
            avg_overall = sum(r.overall_score for r in results) / len(results)

            print(f"\n📊 评分统计:")
            print(f"   平均创新性评分: {avg_innovation:.2f}/10")
            print(f"   平均新颖性评分: {avg_novelty:.2f}/10")
            print(f"   平均完整性评分: {avg_completeness:.2f}/10")
            print(f"   平均综合评分: {avg_overall:.2f}/10")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        print("请检查日志文件 patent_analysis.log 获取详细信息")


if __name__ == "__main__":
    main()
