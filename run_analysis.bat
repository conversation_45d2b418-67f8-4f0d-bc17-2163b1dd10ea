@echo off
chcp 65001 >nul
echo ============================================================
echo 专利分析优化工具 - 启动脚本
echo ============================================================
echo.

echo 🔍 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保已安装Python 3.8或更高版本
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 🔍 检查依赖库...
python -c "import google.generativeai, docx, pandas, tqdm" >nul 2>&1
if errorlevel 1 (
    echo ⚠️  检测到缺少依赖库，正在安装...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ 依赖库安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖库安装完成
) else (
    echo ✅ 依赖库检查通过
)
echo.

echo 🔍 运行系统测试...
python test_analyzer.py
if errorlevel 1 (
    echo.
    echo ⚠️  系统测试发现问题，是否继续运行？ (y/n)
    set /p continue=
    if /i not "%continue%"=="y" (
        echo 已取消运行
        pause
        exit /b 1
    )
)
echo.

echo 🚀 启动专利分析工具...
python patent_analyzer_gemini.py

echo.
echo 📋 分析完成！结果保存在以下目录：
echo    - gemini_analysis/   (详细分析报告)
echo    - gemini_optimized/  (优化后的专利内容)
echo    - gemini_reports/    (汇总报告)
echo.

pause
