# 专利分析优化工具 - 基于Google Gemini API

## 功能概述

本工具使用Google Gemini API对专利文档进行智能分析和优化，提供以下功能：

- **技术创新性评估**：分析专利的技术创新程度
- **新颖性分析**：评估专利技术的新颖性水平
- **现有技术对比**：识别和对比相关现有技术
- **技术方案完整性检查**：评估技术方案的逻辑性和可行性
- **优化建议生成**：提供具体的改进建议和优化方案
- **批量处理**：支持批量处理多个专利文档
- **汇总报告**：生成详细的分析汇总报告

## 安装要求

### 1. Python环境
- Python 3.8 或更高版本

### 2. 依赖库安装
```bash
pip install -r requirements.txt
```

### 3. 获取Gemini API密钥
1. 访问 [Google AI Studio](https://makersuite.google.com/app/apikey)
2. 创建新的API密钥
3. 将API密钥配置到 `config.json` 文件中

## 配置说明

### config.json 配置文件
```json
{
  "gemini_api_key": "your_api_key_here",
  "model_name": "gemini-1.5-pro",
  "input_directory": "input",
  "max_retries": 3,
  "delay_between_requests": 1,
  "batch_size": 5
}
```

**配置参数说明：**
- `gemini_api_key`: Gemini API密钥（必填）
- `model_name`: 使用的Gemini模型名称
- `input_directory`: 输入文件夹路径
- `max_retries`: API调用失败时的最大重试次数
- `delay_between_requests`: 请求间隔时间（秒）
- `batch_size`: 批处理大小

## 使用方法

### 1. 准备专利文档
将需要分析的专利文档（.docx格式）放入 `input` 文件夹中。

### 2. 配置API密钥
编辑 `config.json` 文件，设置您的Gemini API密钥：
```json
{
  "gemini_api_key": "your_actual_api_key_here"
}
```

### 3. 运行分析
```bash
python patent_analyzer_gemini.py
```

### 4. 查看结果
分析完成后，结果将保存在以下目录：
- `gemini_analysis/`: 详细分析报告
- `gemini_optimized/`: 优化后的专利内容
- `gemini_reports/`: 汇总报告

## 输出文件说明

### 1. 分析报告 (gemini_analysis/)
每个专利生成一个详细的分析报告，包含：
- 评分结果（创新性、新颖性、完整性、综合评分）
- 技术优势分析
- 存在不足识别
- 具体改进建议
- 详细分析总结

### 2. 优化内容 (gemini_optimized/)
为每个专利生成优化后的内容，包含：
- 改进的技术描述
- 增强的创新点表达
- 完善的技术方案
- 优化的专利结构

### 3. 汇总报告 (gemini_reports/)
生成Excel和Markdown格式的汇总报告，包含：
- 所有专利的评分对比
- 排名分析
- 统计信息
- 整体评估

## 评分标准

### 技术创新性 (0-10分)
- 9-10分：突破性创新，技术领先
- 7-8分：显著创新，技术先进
- 5-6分：一般创新，技术改进
- 3-4分：微小创新，技术组合
- 1-2分：缺乏创新，现有技术

### 新颖性 (0-10分)
- 9-10分：完全新颖，无相似技术
- 7-8分：高度新颖，差异显著
- 5-6分：中等新颖，有一定差异
- 3-4分：新颖性一般，差异较小
- 1-2分：新颖性不足，相似技术较多

### 完整性 (0-10分)
- 9-10分：方案完整，逻辑清晰，可行性强
- 7-8分：方案较完整，逻辑合理
- 5-6分：方案基本完整，有待完善
- 3-4分：方案不够完整，逻辑有缺陷
- 1-2分：方案不完整，可行性差

## 注意事项

### 1. API使用限制
- 注意Gemini API的调用频率限制
- 大量文档处理时建议适当增加请求间隔
- 监控API使用量，避免超出配额

### 2. 文档格式要求
- 支持.docx格式的专利文档
- 确保文档内容完整，格式规范
- 避免包含大量图片或特殊格式

### 3. 结果准确性
- AI分析结果仅供参考，需要专业人员验证
- 建议结合人工审核进行最终评估
- 不同类型的专利可能需要调整评估标准

## 故障排除

### 1. API密钥问题
```
错误：未配置Gemini API密钥
解决：检查config.json中的gemini_api_key设置
```

### 2. 网络连接问题
```
错误：Gemini API调用失败
解决：检查网络连接，确认API服务可用
```

### 3. 文档读取问题
```
错误：无法读取文件内容
解决：确认文档格式正确，文件未损坏
```

### 4. 内存不足
```
错误：处理大文档时内存不足
解决：减少batch_size，分批处理文档
```

## 技术支持

如遇到问题，请查看：
1. 日志文件：`patent_analysis.log`
2. 错误信息和堆栈跟踪
3. API响应状态和错误代码

## 版本更新

### v1.0.0
- 初始版本发布
- 支持基本的专利分析功能
- 集成Gemini API
- 提供批量处理和报告生成功能
