#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专利分析工具测试脚本
用于验证工具的基本功能
"""

import os
import sys
import json
from pathlib import Path

def test_dependencies():
    """测试依赖库是否正确安装"""
    print("🔍 检查依赖库...")
    
    required_packages = [
        'google.generativeai',
        'docx',
        'pandas',
        'tqdm'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少以下依赖库: {', '.join(missing_packages)}")
        print("请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖库已正确安装")
    return True

def test_config():
    """测试配置文件"""
    print("\n🔍 检查配置文件...")
    
    config_file = Path("config.json")
    if not config_file.exists():
        print("❌ 配置文件 config.json 不存在")
        return False
    
    try:
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查必要的配置项
        required_keys = ['gemini_api_key', 'model_name', 'input_directory']
        missing_keys = []
        
        for key in required_keys:
            if key not in config:
                missing_keys.append(key)
            else:
                print(f"  ✅ {key}: {config[key] if key != 'gemini_api_key' else '***'}")
        
        if missing_keys:
            print(f"❌ 配置文件缺少以下配置项: {', '.join(missing_keys)}")
            return False
        
        # 检查API密钥
        if not config.get('gemini_api_key'):
            print("⚠️  警告: 未设置Gemini API密钥")
            print("   请在config.json中设置gemini_api_key")
            return False
        
        print("✅ 配置文件检查通过")
        return True
        
    except json.JSONDecodeError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def test_input_directory():
    """测试输入目录"""
    print("\n🔍 检查输入目录...")
    
    input_dir = Path("input")
    if not input_dir.exists():
        print("❌ 输入目录 'input' 不存在")
        return False
    
    # 查找docx文件
    docx_files = list(input_dir.glob("*.docx"))
    if not docx_files:
        print("⚠️  警告: 输入目录中没有找到.docx文件")
        print("   请将专利文档放入input文件夹")
        return False
    
    print(f"✅ 找到 {len(docx_files)} 个专利文档:")
    for file in docx_files[:5]:  # 只显示前5个
        print(f"   - {file.name}")
    if len(docx_files) > 5:
        print(f"   ... 还有 {len(docx_files) - 5} 个文件")
    
    return True

def test_output_directories():
    """测试输出目录创建"""
    print("\n🔍 检查输出目录...")
    
    output_dirs = ['gemini_analysis', 'gemini_optimized', 'gemini_reports']
    
    for dir_name in output_dirs:
        dir_path = Path(dir_name)
        if dir_path.exists():
            print(f"  ✅ {dir_name} (已存在)")
        else:
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"  ✅ {dir_name} (已创建)")
            except Exception as e:
                print(f"  ❌ {dir_name} (创建失败: {e})")
                return False
    
    print("✅ 输出目录检查通过")
    return True

def test_api_connection():
    """测试API连接（可选）"""
    print("\n🔍 测试API连接...")
    
    try:
        # 尝试导入并初始化
        import google.generativeai as genai
        
        # 读取配置
        with open("config.json", 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        api_key = config.get('gemini_api_key')
        if not api_key:
            print("⚠️  跳过API连接测试（未配置API密钥）")
            return True
        
        # 配置API
        genai.configure(api_key=api_key)
        
        # 尝试创建模型
        model = genai.GenerativeModel(config.get('model_name', 'gemini-1.5-pro'))
        
        # 简单测试
        response = model.generate_content("Hello, this is a test.")
        
        if response.text:
            print("✅ API连接测试成功")
            return True
        else:
            print("⚠️  API连接测试失败（无响应）")
            return False
            
    except Exception as e:
        print(f"⚠️  API连接测试失败: {e}")
        print("   这可能是由于网络问题或API密钥错误")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("专利分析工具 - 系统测试")
    print("=" * 60)
    
    tests = [
        ("依赖库检查", test_dependencies),
        ("配置文件检查", test_config),
        ("输入目录检查", test_input_directory),
        ("输出目录检查", test_output_directories),
        ("API连接测试", test_api_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 执行失败: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！工具已准备就绪")
        print("\n运行命令: python patent_analyzer_gemini.py")
    else:
        print("⚠️  部分测试未通过，请检查上述问题")
        print("   解决问题后重新运行测试")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
