__虹桥专利技术交底书__

专利类型

<a id="复选框型1"></a>发明     实用新型

发明名称/实用新型名称

### 一种基于H2B的服务器多配置动态加载与自适应调整方法及系统

全部发明人姓名

曹轲、张彩霞

第一发明人

国籍

中国

身份证号

第一申请人

姓名或名称

申请人类别

大专院校 科研单位 工矿企业         事业单位 个人

组织机构代码/个人身份证号

国籍/注册国家（地区）

地址

邮政编码

经常居所地/营业所所在地

第二申请人

姓名或名称

申请人类别

大专院校 科研单位 工矿企业         事业单位 个人

组织机构代码/个人身份证号

国籍/注册国家（地区）

地址

邮政编码

经常居所地/营业所所在地

更多申请人

同上

交底书注意事项：

1、英文缩写应有中文译文，避免使用英文单词；避免使用商品型号、企业自己的代号等等。

2、全文对同一物质的叫法应统一，避免出现一种东西多种叫法。

3、交底书中的术语应为书面术语（即教科书或技术手册规定的术语），不能为行业术语或发明人自己编写的术语（特殊情况下，必需使用时，应在交底资料中指出其为编写的术语）。

__一、技术领域__

本发明属于计算机硬件技术领域，具体涉及服务器系统配置管理技术，尤其是一种利用BMC（Baseboard Management Controller，基板管理控制器）非易失性存储空间存储配置信息，并通H2B（Host to BMC，主机对基板控制器）共享空间的方式，完成配置传递，实现服务器硬件资源动态加载和自适应调整的方法及系统。

__二、详细介绍技术背景，并描述已有的与本发明最接近的实现方案__

服务器作为数据中心的核心计算单元，其硬件配置的灵活性和可管理性对于满足不同应用场景的需求至关重要。在传统的服务器设计和部署中，硬件配置通常在生产制造阶段或初次部署时被固定下来，或者需要通过复杂的手动操作（如跳线设置、BIOS手动修改）进行调整。

技术演进过程：

早期服务器的配置较为固定，主要依赖物理跳线（Jumper）或拨码开关（DIP Switch）来设定有限的硬件参数，如CPU频率、总线速度等。这种方式操作繁琐，易出错，且配置选项有限。

随着技术发展，BIOS Setup Utility成为主要的配置工具。用户在服务器启动时进入BIOS设置界面，可以对CPU、内存、存储、外设等进行配置。然而，这种方式仍需人工干预，对于大规模部署或需要频繁变更配置的场景，效率低下且管理成本高。

为了解决批量配置问题，出现了一些基于预设配置文件的部署工具。这些工具通常在操作系统层面或通过带外管理接口（如IPMI）进行，但它们主要关注的是软件配置或较高层次的硬件参数设置，对于底层的、需要在系统启动早期就确定的硬件资源分配（如PCIe通道分配、特定硬件模块的使能）能力有限。

在一些高级服务器中，BMC扮演了重要的管理角色。BMC可以在主系统启动前独立运行，管理硬件状态、电源、散热等。部分BMC具备一定的配置能力，例如通过IPMI传递一些参数给BIOS。然而，现有技术中，BMC通常不直接参与多套完整硬件配置方案的动态加载。

现有技术中，服务器配置通常是单一的，或者切换配置的过程较为复杂，缺乏一种在系统启动早期，根据预设的多套完整配置方案，自动、快速地调整硬件资源（如带宽分配、设备使能状态）的机制。例如，一台服务器可能根据应用需求，时而被配置为计算密集型（需要更多CPU核心和内存带宽），时而被配置为I/O密集型（需要更多PCIe带宽给网卡或存储卡）。现有技术难以在不进行大规模硬件改动或复杂手动重配的情况下，高效实现这种动态切换。

现有技术流程图：

![](data:image/png;base64,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)__三、现有技术的缺点是什么？针对这些缺点，说明本发明的目的，也即要解决的技术问题__

现有技术缺点：

1. 配置灵活性差，重新配置耗时

传统服务器若需更改底层硬件配置（如PCIe通道分配），往往需要停机、打开机箱进行物理操作（如更换Riser卡、调整跳线）或繁琐的BIOS逐项设置，对于拥有大量服务器的数据中心，这种操作的时间成本极高。假设单台服务器手动重配（包括停机、开箱、设置、测试、上线）平均耗时 $T\_\{manual\}$，需要重配 $N\_\{servers\}$ 台服务器，则总耗时：

$T\_\{total\\\_reconfig\} = N\_\{servers\} \\times T\_\{manual\}$

KaTeX: \`T\_\{total\\\_reconfig\} = N\_\{servers\} \\times T\_\{manual\}\`

例如，若 $T\_\{manual\} = 2$ 小时， $N\_\{servers\} = 100$ 台，则总耗时200小时，这在快速变化的业务需求面前是不可接受的。

1. 资源利用率不均衡，带宽浪费或不足

由于配置固定，服务器可能针对某种峰值负载设计，但在大部分时间，该配置下的资源（如特定端口的带宽）并未得到充分利用，造成浪费。反之，如果配置较低，则在高峰期可能出现性能瓶颈。带宽不匹配度 $\\Delta B\_\{mismatch\}$ 可以表示为：

$\\Delta B\_\{mismatch\} = |B\_\{allocated\} \- B\_\{required\\\_actual\}|$

KaTeX: \`\\Delta B\_\{mismatch\} = |B\_\{allocated\} \- B\_\{required\\\_actual\}|\`

其中 $B\_\{allocated\}$ 是固定配置下的分配带宽，$B\_\{required\\\_actual\}$ 是实际动态需求的带宽。这种不匹配导致潜在的成本增加或性能损失。

1. 人工配置错误率高

手动配置过程，尤其是在BIOS中进行多项复杂设置时，极易引入人为错误。若每个参数的配置错误概率为 $p\_e$，共有 $k$ 个需要配置的参数，则至少发生一次错误的系统配置概率 $P\_\{error\\\_system\}$ 为：

$P\_\{error\\\_system\} = 1 \- \(1 \- p\_e\)^k$

KaTeX: \`P\_\{error\\\_system\} = 1 \- \(1 \- p\_e\)^k\`

随着参数数量 $k$ 的增加，系统配置出错的概率显著上升，导致系统不稳定或性能未达预期。

1. 缺乏统一的多配置识别和应用机制

现有技术中，BMC和BIOS之间的协作通常不是为了实现多套完整硬件配置文件的动态加载。BMC可能管理电源，BIOS负责启动，它们之间缺乏一个有效的信息传递机制来在系统启动的极早期根据选定配置方案动态调整硬件资源。

1. 丝印匹配困难

多配置的服务器场景，由于配置的多样性，丝印根据配置也存在多样性，BIOS无法根据实际硬件资源，准确上报丝印信息，往往需要对配置做全量配置，开发量大切不易于维护

发明的目的：

针对上述现有技术的缺点，本发明旨在提供一种服务器多配置动态加载与自适应调整方法及系统，以解决以下技术问题：

1\.  提高服务器硬件配置的灵活性和自动化程度，缩短配置切换时间。

2\.  根据预设的多种配置方案优化硬件资源（特别是带宽）的分配，提高资源利用率，满足不同应用场景需求。

3\.  减少人工配置错误，提高系统配置的准确性和可靠性。

4\.  实现一种在BMC启动阶段即可读取配置，并协同BIOS在系统启动阶段动态完成硬件自适应调整的机制。

5\.  提供一种快速适配的机制，当客户有新配置需求时，仅通过配置文件即可快速适配。

__四、本发明技术方案的详细阐述__

本发明的核心思想是：预先在BMC的存储介质中，开辟一块非易失性空间，早期制造环节写入默认机型配置。服务器启动时，BMC首先读取flash区域的非易失性块数据，将数据存入和BIOS交互的共享H2B空间，BIOS在后续的POST过程中根据这些信息完成设备枚举、资源分配，丝印和系统信息上报（如SMBIOS表），从而实现服务器硬件的动态、自适应配置。

详细步骤：

步骤一：配置剖面定义与flash非易失空间刷写

1\.  定义配置剖面 \(Configuration Profiles\)：

根据服务器目标应用场景（如计算密集型、存储密集型、网络密集型、均衡型等），定义多套硬件配置剖面。每个剖面包含一组参数，例如：

CPU配置：核心数启用/禁用、超线程技术开关、功耗策略。

内存配置：频率、通道模式、NUMA设置。

PCIe资源分配：

特定PCIe插槽的通道数（如x16, x8, x4）。

板载设备（如网卡、SATA控制器）的PCIe通道分配。

PCIe Bifurcation（通道拆分）设置。

网络带宽配置：

板载网口速率（10G, 25G, 40G, 100G）。

网口模式（如Eth, InfiniBand）。

与CPLD相关的特定网络PHY芯片的模式配置。

存储配置：

SATA/NVMe控制器模式（AHCI, RAID）。

特定硬盘笼的电源和信号使能。

设备使能/禁用：使能或禁用某些不必要的板载设备以节省资源或降低功耗。

版本号和校验和：用于确保配置数据的完整性和正确性。

2\.  非易失空间配置结构设计：

配置文件内可以设计一个头部区域，包含：

魔术字 \(Magic Word\)：标识这是一个有效的配置。

版本信息 \(Version\)。

配置剖面数量 \(Number of Profiles\)。

每个配置剖面的大小 \(Size of each Profile\)。

配置剖面索引表 \(Profile Index Table\)：包含每个剖面ID及其在EEPROM中的起始地址和校验和。

其后是各个配置剖面的实际数据。

例如，一个简化的剖面数据结构 \(Profile Data Structure\) PDS：

$PDS = \\\{ID\_\{prof\}, V\_\{cpu\}, C\_\{mem\}, Map\_\{PCIe\}, C\_\{net\}, C\_\{stor\}, Flags\_\{dev\}, CRC\\\}$

KaTeX: \`PDS = \\\{ID\_\{prof\}, V\_\{cpu\}, C\_\{mem\}, Map\_\{PCIe\}, C\_\{net\}, C\_\{stor\}, Flags\_\{dev\}, CRC\\\}\`

其中 $ID\_\{prof\}$ 是剖面ID，$V\_\{cpu\}$ 是CPU相关配置参数集合，$C\_\{mem\}$ 是内存配置参数集合，$Map\_\{PCIe\}$ 是PCIe映射表，$C\_\{net\}$ 是网络配置参数集合，$C\_\{stor\}$ 是存储配置参数集合，$Flags\_\{dev\}$ 是设备使能标志位集合，$CRC$ 是该剖面的校验和。

3\.  配置的刷写：

选择容量足够的Flash芯片。在服务器生产阶段或部署前，BMC自身提供的对外接口，将定义好的多套配置剖面数据烧写Flash芯片的非易失性区域。非易失性区域BMC控制开辟，固件在线刷新对该区域不做刷新，仅提供配置信息存储使用。

步骤二：配置选项与配置读取

1\.  配置选择机制：

可以通过以下几种方式之一来选择本次启动要加载的配置剖面：

BMC提供对外接口：BMC提供一个对外接口，来配置要选择加载的配置。

BMC预设值：BMC固件中可以预设一个默认的配置剖面ID。

Fash非易失性空间：Flash上开辟一块非易失性空间，存储配置信息，并将该空间挂载到BMC的文件系统。

2\.  BMC启动与配置读取：

服务器上电后，BMC首先启动。在其初始化过程中：

BMC根据上述选择机制确定目标配置剖面ID \( $ID\_\{target\\\_prof\}$ \)。

BMC读取文件系统中配置剖面ID对应的配置信息。

BMC首先读取头部信息和配置剖面索引表，验证其有效性。

根据 $ID\_\{target\\\_prof\}$ 从索引表中查找到对应配置剖面数据在文件系统中的配置位置。

BMC对读取的配置剖面数据进行校验和验证，确保数据未损坏。若校验失败，则可以加载一个预设的“安全模式”或“默认”配置，并记录错误日志。

BMC将完整的配置剖面数据写入H2B空间。

步骤三：BIOS完成配置识别

1\.  BIOS解析通信数据：

BIOS解析配置剖面数据，提取出需要BIOS知晓和处理的信息。这包括但不限于：

当前生效的配置剖面ID和描述。

CPU、内存的详细配置状态（如果BIOS自身无法完全探测或需要BMC辅助确认）。

PCIe设备的配置信息（如哪些插槽被使能、通道数、实际连接的设备类型预期）。

需要特殊初始化的设备列表及其参数。

用于更新SMBIOS表的信息（如系统型号、部件信息等，可以根据配置剖面动态变化）。

2\.  BIOS适配与信息上报：

BIOS在POST过程中：

根据解析的信息调整其设备扫描、枚举和初始化流程。例如，如果信息表明某个PCIe插槽被配置为x4模式，BIOS将按x4模式初始化该插槽上的设备，即使物理上该插槽支持x16。

正确识别和配置丝印。

更新SMBIOS表（System Management BIOS tables）。SMBIOS表存储了系统硬件的详细信息，操作系统和管理软件通过读取SMBIOS表来了解系统配置。BIOS根据当前生效的配置剖面，动态填充SMBIOS中的相关字段，如机箱类型（可根据配置角色变化）、插槽信息、板载设备信息等。例如，Type 9 System Slot Information表中的Current Usage字段可以反映该插槽当前实际分配的通道数。

根据配置剖面中的特定要求，执行其他初始化操作，如设置引导顺序、调整电源管理参数等。

步骤五：系统引导与运行

1\.  BIOS根据BMC提供的信息完成逻辑层面的配置和信息上报。

2\.  操作系统启动后，将识别到一个根据所选配置剖面“定制”的硬件平台。

3\.  应用程序在适配当前硬件配置的服务器上运行，实现最佳性能或资源配比

__五、本发明的关键点和欲保护点__

本发明的关键点：

核心技术特征：

1. Flash非易失性存储：一种通过固件控制，以Flash为介质的永久非易失性存储方式
2. BMC主导的配置加载与分发：BMC在系统主CPU进行完整BIOS POST之前，负责读取选定的配置剖面，并主动将解析后的配置参数分发给BIOS。
3. BIOS基于接收信息的自适应与上报：BIOS根据从BMC获取的配置信息，调整其设备初始化、资源分配过程，并准确更新SMBIOS等系统信息表，以反映当前生效的硬件配置。
4. 快速协同开发：新配置无需BIOS做适配开发，仅需在BMC的非易失性空间导入目标配置

欲保护点：

1. 通过固件控制在Flash区域开辟非易失性空间
2. 配置剖面数据格式
3. BMC和BIOS剖面数据交互方式
4. 配置的选择方式
5. BMC和BIOS根据剖面数据配置识别的分工边界

六、与第二项所述的最相近的现有技术相比，本发明的优点

1. 配置切换时间

现有技术：数小时至数天\(涉及手动硬件调整、BIOS设置、停机\)

本发明技术：数秒至数分钟 \(BMC重启或系统重启即可自动加载新配置\)

量化优势：切换时间缩短 >90% \(例如，从2小时到2分钟\)

1. 硬件资源利用率

现有技术：固定配置，难以完美匹配动态需求，可能偏低 \(如60\-70%\)

本发明优势：根据应用动态选择最优配置，资源利用率高 \(如85\-95%\)

量化优势: 带宽匹配度提升，资源利用率提升约20\-30%

1. 配置准确性/错误率

现有技术：依赖人工操作，错误率较高 \(如 P\_error\_system 可能达到5\-10%\)

本发明优势：自动化加载预验证配置，错误率极低 \(接近0%\)

量化优势：人为配置错误率降低近100%

1. 可扩展性

现有技术：低，每台服务器需单独配置或复杂脚本支持

本发明技术：高，通过选择机制可快速部署不同配置的服务器集群

量化优势：批量部署效率提升一个数量级以上

1. 灵活性与场景适应性

现有技术：低，一种硬件配置难以适应多种业务场景

本发明技术：高，可为同一物理服务器定义多种逻辑配置，快速切换

量化优势：同一硬件平台可支持N种业务场景，N为剖面数量

1. 运维与开发成本

现有技术：高，需要专业人员现场操作，维护复杂，新配置需要开发进行配置开发

本发明技术：低，可远程或通过简单机制切换配置，简化运维，新配置仅用导入配置剖面信息，不涉及开发

七、针对第四部分中的技术方案，是否还有别的替代方案同样能实现发明目的？

替代实施方案一： 存储介质方式替代

实施方式：不使用Flash非易失性存储方式，而是使用eerpom，EMMC，等额外硬件存储介质

替代实施方案二：交互方式替代

实施方式：使用Redfish，IPMI的方式实现配置剖面信息的传递，该方式依赖于请求应答的方式实现，BMC和BIOS具有耦合性

替代实施方案三：数据格式替代

实施方式：不通过配置文件的形式做配置选择，而是通过结构体的方式，承载数据形式。

__八、其他有助于代理人理解本技术的资料__
