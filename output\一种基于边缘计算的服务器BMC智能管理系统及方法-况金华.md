__虹桥专利技术交底书__

专利类型

<a id="复选框型1"></a>发明     实用新型

发明名称/实用新型名称

### 一种基于边缘计算的服务器BMC智能管理系统及方法

全部发明人姓名

况金华

第一发明人

国籍

中国

身份证号

532626198604101952

第一申请人

姓名或名称

申请人类别

大专院校 科研单位 工矿企业         事业单位 个人

组织机构代码/个人身份证号

国籍/注册国家（地区）

地址

邮政编码

经常居所地/营业所所在地

第二申请人

姓名或名称

申请人类别

大专院校 科研单位 工矿企业         事业单位 个人

组织机构代码/个人身份证号

国籍/注册国家（地区）

地址

邮政编码

经常居所地/营业所所在地

更多申请人

同上

交底书注意事项：

1、英文缩写应有中文译文，避免使用英文单词；避免使用商品型号、企业自己的代号等等。

2、全文对同一物质的叫法应统一，避免出现一种东西多种叫法。

3、交底书中的术语应为书面术语（即教科书或技术手册规定的术语），不能为行业术语或发明人自己编写的术语（特殊情况下，必需使用时，应在交底资料中指出其为编写的术语）。

__一、技术领域__

属于服务器管理技术领域，涉及一种基于边缘计算的服务器基板管理控制器（BMC）智能管理系统及方法，适用于数据中心、云计算等场景的服务器硬件状态监控与优化。

__二、详细介绍技术背景，并描述已有的与本发明最接近的实现方案__

1\. 技术背景

服务器基板管理控制器（BMC）作为服务器硬件管理的核心组件，承担着至关重要的职责。在数据中心、云计算、企业服务器集群等场景中，BMC 是实现服务器远程管理、监控与维护的关键技术。它能够独立于服务器主处理器（CPU）运行，通过专用网络连接，实时采集服务器硬件状态信息，如温度、电压、风扇转速、内存错误等，并支持远程开关机、重启、固件升级等操作，极大地提升了服务器运维的便捷性和效率。​

随着数据中心规模的不断扩大和服务器性能需求的持续增长，BMC 面临着诸多挑战。一方面，服务器硬件复杂度日益提升，传感器数量和数据量大幅增加，传统 BMC 的数据处理能力逐渐难以满足需求。大量原始传感器数据需要传输到外部管理平台进行分析，不仅占用网络带宽，还导致数据处理延迟，无法及时对硬件异常做出响应。另一方面，在能耗管理方面，传统 BMC 缺乏灵活的策略调整机制。其固定的传感器采样频率和硬件控制逻辑，无法适应服务器负载的动态变化，造成能源浪费，增加了数据中心的运营成本。此外，面对复杂多变的硬件故障场景，传统 BMC 主要依赖预设的阈值判断和简单的规则匹配，缺乏智能化的故障诊断和预测能力，难以提前发现潜在故障，保障服务器的稳定运行。

2\. 已有的与本发明最接近的实现方案

目前，与本发明技术思路较为接近的现有方案主要围绕 BMC 功能扩展和数据处理优化展开。其中一种常见的实现方式是在 BMC 基础上增加简单的数据预处理模块，对传感器数据进行初步过滤和压缩后再传输至外部管理平台。例如，部分 BMC 通过设定固定的数据过滤规则，去除重复或无效数据，以减少数据传输量。这种方案在一定程度上缓解了网络传输压力，但数据处理能力仍然有限，无法实现对数据的深度分析和智能决策。​

另一种相对先进的方案是将 BMC 与外部智能管理系统相结合，利用云端的计算资源对 BMC 采集的数据进行分析。该方案通过在云端部署机器学习模型，对服务器硬件状态数据进行分析和预测，然后将决策指令返回给 BMC 执行。然而，这种方案存在明显的局限性。首先，数据传输到云端再返回 BMC 的过程会产生较大延迟，无法满足实时性要求较高的硬件控制场景。其次，过度依赖云端计算资源，增加了网络通信成本和数据安全风险，并且在网络不稳定或断连的情况下，系统的可靠性和自主性将受到严重影响。​

此外，还有部分方案尝试在 BMC 中集成一些简单的智能算法，如基于规则的决策引擎，通过预设的硬件状态阈值和操作规则，实现对风扇转速、电源状态的自动调节。但这些规则往往是静态的，无法根据服务器运行状态的动态变化进行自适应调整，缺乏灵活性和智能性，难以有效应对复杂的硬件管理需求。​

__三、现有技术的缺点是什么？针对这些缺点，说明本发明的目的，也即要解决的技术问题__

现有技术中，存在以下技术缺陷：

1\.数据处理低效且依赖外部资源：传统 BMC 仅采集原始传感器数据并简单转发，复杂分析（如故障预测）需依赖服务器主处理器或外部管理平台，导致主处理器负载高、响应延迟长（关键指令滞后数秒），且网络传输压力大。

2\.能耗管理粗放：传感器采样频率固定，低负载时高频采样造成能耗浪费，高负载时因采样不足导致异常漏检测；硬件控制策略僵化，无法精细化调节。

3\.智能化与自主性缺失：仅能执行预设阈值规则（如 “温度超 85℃启动高速风扇”），缺乏基于历史数据的故障预测能力（平均故障发现延迟数十分钟），且依赖外部指令执行操作，网络中断或主处理器宕机时无法独立处理复杂故障。

本实用新型主要通过在 BMC 中集成边缘计算模块与轻量化 AI 技术，实现 “采集 \- 分析 \- 决策” 本地化闭环，具体解决：

1\.提升本地处理能力：利用边缘计算单元在 BMC 内实时运行 AI 模型，完成数据特征提取与故障预测，减少数据上报量，关键决策延迟从秒级降至毫秒级，降低主处理器负载与网络压力。

2\.动态优化能耗与监控精度：根据实时负载动态调整传感器采样频率，结合模糊控制算法实现风扇转速 1% 步进调节，降低传感器能耗、风扇能耗，同时提升异常捕捉能力。

3\.增强智能决策与系统自主性：通过 AI 模型学习硬件正常波动模式，提前数小时预警潜在故障，并自动触发冗余硬件切换，将故障处理时间缩短至5分钟内；支持离线智能模式，在外部中断时独立执行动态管理策略，保障服务器稳定运行。

__四、__<a id="OLE_LINK6"></a>__本发明技术方案的详细阐述__

1、整体技术架构​

本发明构建了 "边缘计算赋能的 BMC 本地化智能管理架构"，在传统 BMC 硬件基础上嵌入边缘计算模块，通过软硬件协同实现数据采集、实时分析、智能决策的全流程闭环。系统架构如图 1 所示，核心由边缘计算硬件层、智能算法软件层、硬件控制接口层三大模块组成，支持与服务器主处理器（CPU）、外部管理平台的双向通信。​

![](data:image/png;base64,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)1. 硬件技术方案​

（1）边缘计算增强型 BMC 芯片设计​

①核心处理器：采用ARM Cortex\-A7\+M4 异构双核架构（或等效 RISC\-V 架构），其中 Cortex\-A7 核心（1\.2GHz 主频）专用于边缘计算任务（AI 模型推理、策略计算），M4 核心（400MHz 主频）负责实时控制（传感器调度、PWM 信号生成），实现计算任务分离。​

②存储系统：​片上集成256MB\+ DDR4 内存（用于 AI 模型加载与中间数据缓存）\+ 8GB eMMC Flash（存储预训练模型、历史数据、控制策略库）；支持动态内存分配技术，根据负载自动调整边缘计算与实时控制的内存占用比例（默认 7:3）。​

③传感器接口：​扩展多协议混合接口：I2C（温度 / 电压传感器）、SPI（风扇转速传感器）、SMBus（电源管理模块）、GPIO（硬件故障信号），支持同时接入≥32 路传感器；​集成12 位 ADC 模数转换器，实现传感器信号的高精度采集（温度精度 ±0\.5℃，电压精度 ±1%）。​

（2）硬件加速单元（可选）​：针对 AI 推理优化，可选配NPU 神经网络加速器，支持 INT8 量化模型加速，将预测推理速度提5倍（单步推理时间＜10μs）。​

1. 软件技术方案​

（1）边缘计算引擎设计​

1. 轻量化 AI 模型部署：​

支持TensorFlow Lite/ONNX Runtime 轻量级框架，预训练模型经模型压缩技术（权重量化、剪枝）后，模型体积≤5MB（如温度预测 LSTM 模型参数压缩至 3\.2MB）；​内置多模型管理机制，可动态加载 / 切换不同场景模型（温度预测模型、风扇能耗优化模型、硬件故障检测模型），默认每 10 分钟根据负载自动选择最优模型。​

1. 数据处理流程：​

预处理层：对原始传感器数据进行去噪（中值滤波）、归一化（Z\-Score标准化），生成多维特征向量（包含当前值、历史5分钟滑动平均值、标准差等）；​

推理层：通过LSTM网络预测未来5分钟硬件状态（如CPU温度波动范围），输出预测置信度（≥90% 视为有效预测）；​

决策层：基于预测结果生成控制指令（如风扇转速调节值、采样频率调整策略），同时将异常数据（置信度＜70%的预测结果）封装为Redfish格式消息上报主处理器。​

1. 动态采样优化算法​

负载感知模型：​定义服务器负载指数（SLI） = 0\.6×CPU 利用率 \+ 0\.3× 内存带宽占用 \+ 0\.1×PCIe 设备吞吐量，实时计算周期 100ms；​当 SLI＜30%（低负载）时，采样频率降至0\.1Hz并启动数据压缩（差分编码，压缩比 4:1）；​当 SLI≥80%（高负载）时，采样频率提升至20Hz并开启实时数据流模式（跳过压缩，优先保证时效性）。​

1. 智能硬件控制策略​
2. 风扇调速算法：

​采用模糊控制 \+ 预测补偿混合策略：

​输入参数：当前 CPU 温度（误差e）、温度变化率（Δe/Δt）、预测温度趋势（ΔT\_pred）；​

输出参数：风扇 PWM 占空比（0\-100%，1% 步进），其中预测补偿项占比 30%（根据 LSTM 预测结果提前调整）；​

噪音控制机制：当负载＜50% 时，风扇转速上限限制为 60%；负载＞80% 时，允许突破至 100%。​②电源管理策略：​

基于硬件健康度模型（通过电压波动、电流谐波等参数计算），当某电源模块健康度＜70% 时，自动触发冗余电源模块预启动（提前5分钟预热），切换过程不中断服务器供电。​

4、通信与接口技术​

（1）内部通信：​与主处理器通过PCIe 4\.0 x1 接口连接，采用自定义二进制协议传输控制指令（单包数据≤64字节，传输延迟＜1μs）；​异常状态下支持带外管理通道IPMI v2\.0，确保主处理器宕机时仍可通过网络远程访问 BMC。​

（2）外部通信：​支持Redfish v1\.6以上标准协议，提供统一 RESTful API 接口，实现与<a id="OLE_LINK7"></a>OpenStack、Zabbix 等管理平台的无缝对接；​数据上报采用事件驱动机制：仅当硬件状态变化超过阈值（如温度变化＞2℃）或预测到异常时主动上报，相比传统轮询模式减少50%无效通信。

__五、本发明的关键点和欲保护点__

异构硬件架构：采用 ARM Cortex\-A7\+M4 双核或等效架构，分离边缘计算与实时控制任务，可增配 NPU，实现低功耗智能处理。

动态决策算法：基于负载指数动态调节传感器采样频率；运用模糊控制与预测补偿优化风扇调速；依据硬件健康度预启动冗余电源。

轻量化 AI 部署：支持 TensorFlow Lite 等框架，经压缩的模型体积≤5MB，可动态切换适应不同场景。

通信优化机制：内部高速低延迟通信，外部采用 Redfish 协议与事件驱动上报，减少无效通信。

自优化闭环：构建预测与反馈双闭环，通过在线增量学习更新策略库，适应长期运行场景 。

六、与第二项所述的最相近的现有技术相比，本发明的优点

本发明在硬件架构、数据处理、能耗管理、决策能力等方面优势显著：

硬件架构革新：采用异构双核架构或等效设计，集成 NPU，实现计算任务分离与低功耗边缘计算，而现有技术缺乏专用计算单元，智能处理依赖外部资源。

数据处理高效：通过轻量化 AI 模型部署与边缘计算引擎，实现数据本地实时分析、预测与决策，无需主处理器深度参与，相比现有技术减少 70% 以上数据上报量，关键决策延迟从秒级降至毫秒级。

能耗管理智能：基于负载动态调整传感器采样频率，结合模糊控制与预测补偿优化风扇调速，降低传感器模块能耗 40%、风扇能耗 25%，现有技术采样频率固定，能耗浪费严重。

决策能力卓越：构建预测 \- 反馈双闭环控制与自优化系统，能提前 4\-6 小时预警硬件故障并自动处理，现有技术依赖预设规则，故障预警滞后，缺乏主动处理能力。

七、针对第四部分中的技术方案，是否还有别的替代方案同样能实现发明目的？

无

__八、其他有助于代理人理解本技术的资料__

1. 服务器虚拟化在网络信息体系中的应用和安全防护\[J\]\. 李超;党增江\.网络安全与数据治理,2023\(01\)
2. 虚拟化技术在校园升级网络中的应用探究\[J\]\. 司海峰;王艳娥;杨倩\.江西通信科技,2022\(03\)
3. 基于云计算的虚拟化技术发展\[J\]\. 闫顺琪;赵永丰;史秀鹏;张宏宇;孙麒;靳力\.网信军民融合,2022\(03\)
