# 文档转换工具

这个工具可以将input目录下的Word文档（.doc和.docx格式）转换为Markdown格式。

## 功能特点

- 支持.docx格式文档转换
- 保留文档的基本格式（标题、段落、表格等）
- 自动清理多余的空行和格式问题
- 提供两种转换方法以确保兼容性
- 详细的转换进度和结果报告

## 安装依赖

在运行脚本之前，请先安装所需的Python包：

```bash
pip install -r requirements.txt
```

或者手动安装：

```bash
pip install python-docx mammoth
```

## 使用方法

1. 将需要转换的Word文档放入`input`目录
2. 运行转换脚本：

```bash
python convert_docs_to_md.py
```

3. 转换后的Markdown文件将保存在`output`目录中

## 目录结构

```
tools/
├── input/                  # 放置待转换的Word文档
├── output/                 # 转换后的Markdown文件（自动创建）
├── convert_docs_to_md.py   # 转换脚本
├── requirements.txt        # Python依赖包列表
└── README.md              # 使用说明
```

## 支持的格式

- ✅ .docx - 完全支持
- ⚠️ .doc - 需要额外配置（建议先转换为.docx）

## 转换质量

脚本使用两种转换方法：

1. **Mammoth库**（主要方法）- 提供更好的格式保留
2. **Python-docx库**（备用方法）- 基础转换功能

## 注意事项

- 确保input目录存在且包含Word文档
- 复杂的格式可能无法完美转换
- 建议在转换后检查Markdown文件的格式
- 对于.doc格式，建议先在Word中另存为.docx格式

## 故障排除

如果遇到转换失败：

1. 检查文档是否损坏
2. 尝试在Word中重新保存文档
3. 确保已安装所有依赖包
4. 查看控制台输出的错误信息 