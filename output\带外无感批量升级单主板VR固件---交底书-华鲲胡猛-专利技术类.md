__虹桥专利技术交底书__

专利类型

√发明     实用新型

发明名称/实用新型名称

一种批量升级单主板VR固件的方法

全部发明人姓名

胡猛

第一发明人

国籍

中国

身份证号

412826199408092217

第一申请人

姓名或名称

申请人类别

大专院校 科研单位 工矿企业         事业单位 个人

组织机构代码/个人身份证号

国籍/注册国家（地区）

地址

邮政编码

经常居所地/营业所所在地

第二申请人

姓名或名称

申请人类别

大专院校 科研单位 工矿企业         事业单位 个人

组织机构代码/个人身份证号

国籍/注册国家（地区）

地址

邮政编码

经常居所地/营业所所在地

更多申请人

同上

交底书注意事项：

1、英文缩写应有中文译文，避免使用英文单词；避免使用商品型号、企业自己的代号等等。

2、全文对同一物质的叫法应统一，避免出现一种东西多种叫法。

3、交底书中的术语应为书面术语（即教科书或技术手册规定的术语），不能为行业术语或发明人自己编写的术语（特殊情况下，必需使用时，应在交底资料中指出其为编写的术语）。

__一、技术领域__

本发明涉及一种带外无感升级服务器多个VR芯片技术，涉及服务器主板、BMC、VR和VR固件压缩包。通过算法识别VR固件压缩包，通过带外手段对主板上的多个VR进行无感升级。

带外无感升级，VR，BMC

__二、详细介绍技术背景，并描述已有的与本发明最接近的实现方案__

现阶段由于当前服务器主板上存在多个VR芯片，这些VR芯片都具有固件。在服务器研发或者上架运维阶段，当VR固件出现问题，需要升级时，只能依次去升级，增加人员操作的复杂度且过程繁琐耗时过长。

与本发明方案最接近的方案CN202310406190\.6。此方案仅仅就VR升级流程做了保护，对于多个VR存在，如何批量升级没有说明。

因此，本发明专利设计一种降低运维成本、操作简单、耗时较短的批量升级新方法。

__三、现有技术的缺点是什么？针对这些缺点，说明本发明的目的，也即要解决的技术问题__

__现有技术的缺点：现有专利技术仅针对服务器VR芯片的远程升级做的保护，但是一个服务器主板上存在多个VR芯片，在服务器主板研发阶段或服务器上架运维阶段，需要升级VR固件，只能依次升级，存在增加人员时间成本且耗时耗力__

__本发明目的：为了解决依次升级多个VR固件，存在增加人员时间成本和耗时耗力。因此采用了一种远程、自识别、批量升级多个VR固件的方法__

__四、本发明技术方案的详细阐述__

前置条件：

1\. 单个VR固件，文件头填入 主板型号、VR芯片位置丝印、厂商

2\. 多个VR固件，采用无损压缩加密方式转换成单个文件

3\. cpld寄存器存储 当前主板VR厂商的BCD编码信息、当前主板型号的BCD编码、VR芯片丝印位置BCD编码

软件算法逻辑：

WEB 上传加密固件，通过lighttpd传递加密固件到BMC内部

1\. 解压无损压缩加密文件为多个VR固件文件

2\. 每个VR固件分配内存空间，读取固件信息到内存空间

读取cpld寄存器主板型号，依次和VR保存主板型号比较，匹配

读取cpld 寄存器中VR具体丝印编码，依次和VR丝印位置比较，并且匹配上

读取cpld 寄存器中 VR厂商和 VR固件 厂商匹配

设置cpld 寄存器，切换对应的VR PMbus到BMC端

通过PMbus读取VR芯片的厂商和型号，与VR固件中的型号和cpld中的厂商信息比较，再次匹配

根据不同厂商的更新要求，进行单个固件更新

单个固件更新完毕，进行下一个芯片匹配更新

全部更新完毕后，WEB提示更新成功，退出更新

__五、本发明的关键点和欲保护点__

关键点：本发明的关键点是软件 利用 主板cpld 和VR芯片，VR固件标签实现主板多VR固件单次更新。提高了主板power on研发人员debug分析问题的效率，和服务器上架运维阶段 降低的运维难度和节约了运维时间成本

预保护点：单主板多个VR 批量升级的软件算法。

六、与第二项所述的最相近的现有技术相比，本发明的优点

七、针对第四部分中的技术方案，是否还有别的替代方案同样能实现发明目的？

__八、其他有助于代理人理解本技术的资料__

给代理人提供更多的信息，可以有助于代理人更好更快地完成申请文件的撰写。
